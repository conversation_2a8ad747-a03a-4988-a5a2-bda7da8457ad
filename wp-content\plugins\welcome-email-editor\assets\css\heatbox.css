/* Init */
.heatbox-wrap,
.heatbox-wrap * {
	-webkit-box-sizing: border-box;
	box-sizing: border-box;
}

.heatbox-admin.has-header .update-nag {
	display: none;
}

.heatbox-admin.has-header #wpcontent {
	padding-left: 0 !important;
	padding-right: 0 !important;
}

.heatbox-admin.has-header .wrap {
	margin: 0;
}

/* Tab navigation */
.heatbox-tab-nav {
	padding: 20px 0;
	margin: 10px 0 0 0;
}

.heatbox-tab-nav li {
	display: inline-flex;
	padding: 10px 30px 10px 0;
	font-weight: 600;
	font-size: 20px;
}

.heatbox-tab-nav li a {
	color: #23282d;
	text-decoration: none;
}

.heatbox-tab-nav li.active a {
	color: #0073aa;
}

/* Divider */
.heatbox-divider {
	width: 100%;
	height: 1px;
	margin: 40px 0;
	background: #ccc;
}

@media screen and (max-width: 991px) {
	.heatbox-divider {
		margin: 20px 0;
	}
}

/* Container */
.heatbox-container {
	max-width: 1200px;
	padding: 0 20px;
}

.heatbox-container-wide {
	max-width: 1400px;
}

.heatbox-container-center {
	margin: 0 auto;
}

/* Header */
.heatbox-header {
	background: #fff;
	padding-top: 40px;
	border-bottom: 1px solid #ddd;
}

.heatbox-header.heatbox-has-tab-nav .logo-container {
	margin-bottom: 0;
}

.heatbox-header .logo-container {
	display: flex;
	align-items: center;
	margin-bottom: 40px;
}

.heatbox-header .logo-container div {
	width: 50%;
}

.heatbox-header .logo-container .heatbox-logo-wide img {
	width: 180px;
}

.heatbox-header .logo-container img {
	width: 100px;
	height: auto;
	float: right;
}

.heatbox-header .tab-navigation {
	margin: -20px 0 20px 0;
	padding: 0;
}

.heatbox-header .tab-navigation li {
	display: inline-flex;
	padding: 10px 30px 10px 0;
	font-weight: 600;
	font-size: 20px;
}

.heatbox-header .tab-navigation li a {
	color: #23282d;
	text-decoration: none;
}

.heatbox-header .tab-navigation li.active a {
	color: #0073aa;
}

/* Title */
.heatbox-wrap .title {
	margin: 0;
	padding: 0;
	font-size: 34px;
	font-weight: 700;
	line-height: 1;
}

.heatbox-wrap .subtitle {
	margin: 20px 0 0 0;
	padding: 0;
	font-size: 20px;
	line-height: 1;
}

/* Version */
.heatbox-wrap .version {
	font-size: 45%;
	opacity: 0.6;
	font-weight: 600;
	background: #ccc;
	border-radius: 5px;
	padding: 5px 12px;
	line-height: 1;
	vertical-align: middle;
	margin: 0 5px;
}

/* 2 Column Layout - Wrapper */
.heatbox-column-container {
	display: flex;
	flex-wrap: wrap;
}

/* 2 Column Layout - Main Content */
.heatbox-main {
	width: 73%;
	margin-right: 2%;
}

/* 2 Column Layout - Sidebar */
.heatbox-sidebar {
	width: 25%;
}

/* 2 Column Layout - Responsiveness */
@media screen and (max-width: 991px) {
	.heatbox-main,
	.heatbox-sidebar {
		width: 100%;
	}

	.heatbox-main {
		margin-right: 0;
	}
}

/* Panels */
.heatbox-admin-panel {
	display: none;
}

/* Heatbox */
.heatbox {
	background: #fff;
	margin-bottom: 20px;
	border: 1px solid #ddd;
}

.heatbox h2 {
	border-bottom: 1px solid #ddd;
	margin: 0;
	padding: 20px;
}

.heatbox.is-grouped h2 {
	border-top: 1px solid #ddd;
}

.heatbox.is-grouped h2:first-child {
	border-top: none;
}

.heatbox h3 {
	margin: 0 0 10px 0;
}

.heatbox p {
	margin: 0 0 20px 0;
}

.heatbox-content {
	margin: 0;
	padding: 20px;
}

.heatbox-content p:first-child {
	margin-top: 0;
}

/* Margins */
.heatbox-margin-top {
	margin-top: 20px;
}

.heatbox-margin-bottom {
	margin-bottom: 20px;
}

/* Paddings */
.heatbox-margin-top {
	margin-top: 20px;
}

.heatbox-margin-bottom {
	margin-bottom: 20px;
}

/* Loading Button */
@-webkit-keyframes heatboxLoadingSpinner {
	from {
		transform: rotate(0);
	}
	to {
		transform: rotate(359deg);
	}
}

@keyframes heatboxLoadingSpinner {
	from {
		transform: rotate(0);
	}
	to {
		transform: rotate(359deg);
	}
}

/* Buttons */
.heatbox-wrap .button-larger {
	padding: 5px 20px;
}

.heatbox-wrap .button-full {
	width: 100%;
	text-align: center;
}

.heatbox-wrap .button.is-loading {
	display: inline-flex;
	align-items: center;
	justify-content: center;
	color: transparent !important;
	pointer-events: none;
}

.heatbox-wrap .button.is-loading::after {
	content: "";
	position: absolute;
	width: 1em;
	display: block;
	height: 1em;
	border: 2px solid #dbdbdb;
	/* border: 2px solid $accent-color; */
	border-color: transparent transparent #fff #fff !important;
	/* border-color: transparent transparent $accent-color $accent-color !important; */
	border-radius: 290486px;
	-webkit-animation: heatboxLoadingSpinner 0.5s infinite linear;
	animation: heatboxLoadingSpinner 0.5s infinite linear;
}

/* Settings */
.heatbox .form-table {
	margin: 0;
}

.heatbox .form-table th,
.heatbox .form-table td {
	padding: 20px;
	vertical-align: top;
}

.heatbox .form-table th .description {
	margin-top: 10px;
}

.heatbox .setting-fields {
}

.heatbox .setting-fields .setting-field {
	margin-bottom: 7px;
}

.heatbox .setting-fields .setting-field:last-of-type {
	margin-bottom: 0;
}

/* Submit button */
.heatbox-wrap p.submit {
	margin: 20px 0;
	padding: 0;
}

/**
 * Checkboxes & Radio Buttons
 * Copied & modified from https://codepen.io/KenanYusuf/pen/PZKEKd/
 */
.heatbox .label {
	display: inline-block;
	position: relative;
	cursor: pointer;
}

.heatbox .radio-label {
	padding-left: 30px;
	margin-right: 25px;
}

.heatbox .label input {
	position: absolute;
	z-index: -1;
	opacity: 0;
}

.heatbox .label .indicator {
	position: absolute;
	top: 0;
	left: 0;
	height: 20px;
	width: 20px;
	background-color: #e6e6e6;
	-webkit-transition: all 0.2s ease-in-out;
	transition: all 0.2s ease-in-out;
}

.heatbox .radio-label .indicator {
	border-radius: 50%;
}

.heatbox .label:hover input ~ .indicator,
.heatbox .label input:focus ~ .indicator {
	background-color: #ccc;
}

.heatbox .label input:checked ~ .indicator {
	background-color: #007cba;
}

.heatbox .label:hover input:not([disabled]):checked ~ .indicator,
.heatbox .label input:checked:focus ~ .indicator {
	background-color: #00669b;
}

.heatbox .label input:disabled ~ .indicator {
	background-color: #e6e6e6;
	opacity: 0.6;
	pointer-events: none;
}

.heatbox .label .indicator::after {
	display: none;
	position: absolute;
	content: "";
}

.heatbox .label input:checked ~ .indicator::after {
	display: block;
}

.heatbox .radio-label,
.heatbox .checkbox-label {
	padding-left: 30px;
}

.heatbox .checkbox-label .indicator::after {
	left: 8px;
	top: 4px;
	width: 3px;
	height: 8px;
	border: solid #fff;
	border-width: 0 2px 2px 0;
	transform: rotate(45deg);
}

.heatbox .checkbox-label input:disabled ~ .indicator::after {
	border-color: #7b7b7b;
}

.heatbox .radio-label .indicator::after {
	left: 7px;
	top: 7px;
	height: 6px;
	width: 6px;
	border-radius: 50%;
	background-color: #fff;
}

.heatbox .radio-label input:disabled ~ .indicator::after {
	background-color: #7b7b7b;
}

/* Call to action */
.heatbox-cta-container.is-attached {
	padding: 20px;
	background: #fff;
	margin-top: -20px;
	margin-bottom: 20px;
	border: 1px solid #ddd;
	border-top: none;
}

.heatbox-cta {
	width: 100%;
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20px;
	font-weight: 700;
	border: 1px solid #ddd;
	background: #fff;
}

.heatbox-cta > * {
	margin: 0 !important;
}

.heatbox-cta .button {
	padding: 5px 15px !important;
}

.heatbox-cta.primary {
	background: #197cff;
	color: #fff;
	border: none;
}

.heatbox-cta.secondary {
	background: #f1f1f1;
}

.heatbox-cta.primary .button {
	background: rgba(0, 45, 96, 0.5);
}

.heatbox-cta.primary .button:hover {
	background: rgba(0, 45, 96, 0.6);
}

/* Toggle switch */
.heatbox-wrap .toggle-switch {
	position: relative;
	display: inline-block;
	width: 36px;
	height: 20px;
}

.heatbox-wrap .toggle-switch input {
	opacity: 0;
	width: 0;
	height: 0;
}

.heatbox-wrap .toggle-switch .switch-track {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: #ccc;
	border-radius: 10px;
	cursor: pointer;
	transition: background-color 0.3s;
}

.heatbox-wrap .switch-thumb {
	position: absolute;
	top: 2px;
	left: 2px;
	width: 16px;
	height: 16px;
	background-color: #fff;
	border-radius: 50%;
	transition: transform 0.3s;
}

.heatbox-wrap .toggle-switch input:checked + .switch-track {
	background-color: #5bcb86;
}

.heatbox-wrap .toggle-switch input:checked + .switch-track .switch-thumb {
	transform: translateX(16px);
}

/* Tags / chips */
.tags-heatbox .action-status {
	height: auto;
}

.tags-heatbox .tags-wrapper {
	line-height: 1.8;
}

.tags-heatbox h2 {
	position: relative;
}

.tags-heatbox h2 .action-status {
	display: inline-block;
	position: absolute;
	right: 20px;
	top: 50%;
	padding: 10px;
	height: 30px;
	line-height: 10px;
	font-size: 11px;
	color: #fff;
	background-color: #444;
	border-radius: 4px;
	opacity: 0;
	visibility: hidden;
	transform: translateY(-50%);
	transition: all 0.3s ease-in-out;
}

.tags-heatbox h2 .action-status.is-shown {
	opacity: 1;
	visibility: visible;
}

.tags-heatbox .heatbox-content p:last-child {
	margin-bottom: 0;
}

.heatbox .tags-wrapper code {
	display: inline-block;
	margin-bottom: 5px;
	padding: 8px 13px;
	background-color: #fff;
	border: 1px solid #ddd;
	border-radius: 3px;
	cursor: pointer;

	&:hover {
		background-color: #f8f8f8;
	}
}

/* Tooltip */
.heatbox-tooltip {
	position: relative;
	cursor: pointer;
	padding: 5px;
}

.heatbox-tooltip .tooltip-content {
	visibility: hidden;
	opacity: 0;
	width: 220px;
	background-color: #fff;
	text-align: center;
	padding: 10px;
	font-size: 12px;
	-webkit-transition: all 0.2s ease-in-out;
	transition: all 0.2s ease-in-out;
	position: absolute;
	left: -100px;
	border: 1px solid #ddd;
	bottom: 23px;
	z-index: 1;
}

.heatbox-tooltip:hover .tooltip-content {
	visibility: visible;
	opacity: 1;
	bottom: 28px;
}

.heatbox-tooltip.has-image .tooltip-content {
	padding: 0;
	text-align: left;
	display: flex;
	align-content: center;
	align-items: center;
	width: 370px;
}

.heatbox-tooltip.has-image .tooltip-content img {
	width: 35%;
	border-right: 1px solid #eee;
}

.heatbox-tooltip.has-image .tooltip-content span {
	width: 65%;
	padding: 20px;
}

/* Misc */
.heatbox .row-disabled {
	background-color: #f5f5f5;
	opacity: 0.5;
}

.heatbox .row-disabled input[type="text"]:read-only,
.heatbox .row-disabled input[type="number"]:read-only {
	background-color: #e0e0e0;
	cursor: not-allowed;
	opacity: 0.7;
}

/* RTL */
.rtl .heatbox-header .logo-container img {
	float: left;
}

.rtl .heatbox-main {
	margin-right: 0;
	margin-left: 2%;
}

.rtl .heatbox .label .indicator {
	left: auto;
	right: -30px;
}
