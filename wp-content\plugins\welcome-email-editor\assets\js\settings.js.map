{"mappings": "A,C,SKiBK,EHKL,SAAS,EAAU,CAAW,EAK7B,AAJoB,SAAS,gBAAgB,CAC5C,2BAGW,OAAO,CAAC,SAAU,CAAO,EACpC,IAAM,EAAW,EAAQ,aAAa,CACtC,GAAI,CAAC,EAAU,OAEf,IAAM,EAAO,EAAQ,IAAI,AAGrB,CAFc,CAAA,EAAO,EAAK,KAAK,CAAC,IAAI,CAAC,EAAE,CAAG,EAA9C,IAEkB,EACjB,EAAS,SAAS,CAAC,GAAG,CAAC,UAEvB,EAAS,SAAS,CAAC,MAAM,CAAC,SAE5B,GAMA,AAJuB,SAAS,gBAAgB,CAC/C,wBAGc,OAAO,CAAC,SAAU,CAAU,EAC1C,IAAM,EAAc,EAAW,OAAO,CAAC,WAAW,CAI9C,AAFqB,AADP,CAAA,EAAc,EAAY,KAAK,CAAC,KAAO,EAAE,AAAF,EACtB,GAAG,CAAC,AAAC,GAAU,EAAM,IAAI,IAEvC,QAAQ,CAAC,GAC7B,EAAW,KAAK,CAAC,OAAO,CAAG,QAE3B,EAAW,KAAK,CAAC,OAAO,CAAG,MAE7B,GAEA,AASD,SAAyB,CAAiB,EACzC,IAAM,EAAe,SAAS,aAAa,CAAC,6BAE5C,GAAI,GACH,GAAI,EAAa,KAAK,CAAC,QAAQ,CAAC,KAAM,CAErC,IAAM,EAAM,AADM,EAAa,KAAK,CAAC,KAAK,CAAC,IACtB,CAAC,EAAE,AAExB,CAAA,EAAa,KAAK,CAAG,EAAM,IAAM,CAClC,MACC,EAAa,KAAK,CAAG,EAAa,KAAK,CAAG,IAAM,EAIlD,IAAM,EAAY,SAAS,aAAa,CAAC,sBAEzC,GAAI,GACH,GAAI,EAAU,IAAI,CAAC,QAAQ,CAAC,KAAM,CAEjC,IAAM,EAAM,AADM,EAAU,IAAI,CAAC,KAAK,CAAC,IAClB,CAAC,EAAE,AAExB,CAAA,EAAU,IAAI,CAAG,EAAM,IAAM,CAC9B,MACC,EAAU,IAAI,CAAG,EAAU,IAAI,CAAG,IAAM,EAG3C,EAnCiB,EACjB,CE1DO,eAAe,EAAgB,CAAY,EACjD,GAAI,CACH,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC,EACrC,CAAE,MAAO,EAAK,CAEb,MAAM,EAA8B,EACrC,CACD,CAEO,eAAe,EAA8B,CAAY,EAC/D,IAAM,EAAW,SAAS,aAAa,CAAC,WAExC,CAAA,EAAS,KAAK,CAAG,EACjB,EAAS,KAAK,CAAC,QAAQ,CAAG,QAC1B,EAAS,KAAK,CAAC,GAAG,CAAG,OACrB,EAAS,KAAK,CAAC,IAAI,CAAG,OACtB,EAAS,KAAK,CAAC,KAAK,CAAG,MACvB,EAAS,KAAK,CAAC,MAAM,CAAG,MACxB,EAAS,KAAK,CAAC,UAAU,CAAG,cAC5B,EAAS,KAAK,CAAC,OAAO,CAAG,IAEzB,SAAS,IAAI,CAAC,WAAW,CAAC,GAC1B,EAAS,KAAK,GACd,EAAS,MAAM,GAEf,GAAI,CACH,SAAS,WAAW,CAAC,OACtB,CAAE,MAAO,EAAK,CACb,QAAQ,KAAK,CAAC,oCAAqC,EACpD,CAEA,SAAS,IAAI,CAAC,WAAW,CAAC,EAC3B,CA2BO,SAAS,EAAW,CAAe,EACzC,EAAG,SAAS,CAAC,GAAG,CAAC,aACjB,EAAG,SAAS,CAAC,MAAM,CAAC,cACpB,EAAG,SAAS,CAAC,MAAM,CAAC,YACpB,EAAG,SAAS,CAAC,MAAM,CAAC,aACpB,EAAG,SAAS,CAAC,MAAM,CAAC,cACpB,EAAG,SAAS,CAAC,MAAM,CAAC,WACpB,EAAG,SAAS,CAAG,EAChB,CGrDC,OAAO,UAAU,EAAE,CAAC,QAAS,8BAG9B,SAAuB,CAAa,EAC9B,QAAQ,aAAa,eAAe,CAAC,aAAa,GACtD,EAAE,cAAc,EAClB,GLfC,AAJoB,SAAS,gBAAgB,CAC5C,2BAGW,OAAO,CAAC,SAAU,CAAO,EACpC,EAAQ,gBAAgB,CAAC,QAAS,SAAU,CAAC,EAC5C,IAAM,EAAO,EAAQ,IAAI,CAEzB,EADkB,EAAO,EAAK,KAAK,CAAC,IAAI,CAAC,EAAE,CAAG,GAE/C,EACD,GAEA,OAAO,gBAAgB,CAAC,OAAQ,SAAU,CAAE,EAC3C,IAAI,EAAO,OAAO,QAAQ,CAAC,IAAI,CAI/B,EAFkB,AADlB,CAAA,EAAO,GAAc,OAArB,EACuB,OAAO,CAAC,IAAK,IAGrC,GDXD,AENO,WACN,AAEA,CAAA,WACC,IAAM,EAAU,SAAS,aAAa,CAAC,iBACvC,GAAI,CAAC,EAAS,OAEd,IAAM,EAAO,EAAQ,gBAAgB,CAAC,OACjC,CAAA,EAAK,MAAM,EAEhB,EAAK,OAAO,CAAC,AAAC,IACb,EAAI,gBAAgB,CAAC,QAAS,EAC/B,EACD,CAAA,IAEA,eAAe,EAAe,CAAQ,EACrC,IAAM,EAAM,EAAE,MAAM,CACpB,GAAI,CAAC,EAAK,OAEV,IAAM,EAAQ,EAAI,SAAS,CAC3B,GAAI,CAAC,EAAO,MAGZ,OAAM,AAAA,EAAgB,GAEtB,IAAM,EAAS,SAAS,aAAa,CACpC,gCAEI,IAEL,EAAO,SAAS,CAAC,GAAG,CAAC,YAErB,WAAW,KACV,EAAO,SAAS,CAAC,MAAM,CAAC,WACzB,EAAG,MACJ,CACD,IF7BA,AKTO,WACN,IAAM,EAAqB,SAAS,gBAAgB,CACnD,4BAGK,EAA6B,EAAE,CAC/B,EAAuB,EAAE,CACzB,EAAgC,EAAE,CA0DxC,SAAS,EAAgC,CAAQ,EAChD,IAAM,EAAQ,EAAG,MAAM,CAAsB,KAAK,CAC5C,EAAgB,SAAS,aAAa,CAAC,qCACxC,IAED,AAAU,QAAV,EACH,EAAc,KAAK,CAAG,MACZ,AAAU,QAAV,EACV,EAAc,KAAK,CAAG,MAEtB,EAAc,KAAK,CAAG,KAExB,CApEA,EAAmB,OAAO,CAAC,SAAU,CAAiB,EACrD,IACM,EAAY,AADO,EAAkB,OAAO,CAAC,eAAe,EACV,GACxD,GAAI,CAAC,EAAW,OAEhB,IAAM,EAAQ,SAAS,aAAa,CACnC,UAAY,EAAY,MAEpB,IAEA,EAAW,QAAQ,CAAC,KACxB,EAAW,IAAI,CAAC,GAChB,EAAO,IAAI,CAAC,IAGb,EAAe,IAAI,CAAC,GAEhB,EAAM,OAAO,CAChB,EAAkB,KAAK,CAAC,OAAO,CAAG,QAElC,EAAkB,KAAK,CAAC,OAAO,CAAG,OAEpC,GAEA,EAAO,OAAO,CAAC,SAAU,CAAK,EAC7B,EAAM,gBAAgB,CAAC,SAAU,SAAU,CAAC,EACvC,EAAM,OAAO,CAChB,EAAe,OAAO,CAAC,SAAU,CAAa,EAC7C,IACM,EAAY,AADO,EAAc,OAAO,CAAC,eAAe,EACN,GACnD,GAED,IAAc,EAAM,IAAI,EAC3B,CAAA,EAAc,KAAK,CAAC,OAAO,CAAG,OAD/B,CAGD,GAEA,EAAe,OAAO,CAAC,SAAU,CAAa,EAC7C,IACM,EAAY,AADO,EAAc,OAAO,CAAC,eAAe,EACN,GACnD,GAED,IAAc,EAAM,IAAI,EAC3B,CAAA,EAAc,KAAK,CAAC,OAAO,CAAG,MAD/B,CAGD,EAEF,EACD,GAIA,AAF6B,SAAS,gBAAgB,CAAC,2CAElC,OAAO,CAAC,SAAU,CAAmB,EACzD,EAAoB,gBAAgB,CAAC,SAAU,EAChD,EAeD,ID7DK,EAAe,CAAA,EAMlB,OAAO,UAAU,EAAE,CAAC,QAAS,0BAM9B,SAAuB,CAAa,EACnC,EAAE,cAAc,GAGhB,IAAM,EAAS,IAAI,CACnB,GAAI,CAAC,EAAQ,OAIb,AAFqB,SAAS,gBAAgB,CAAC,2BAElC,OAAO,CAAC,SAAU,CAAE,EAChC,AAAA,EAAW,EACZ,GAEA,IAAM,EAAW,EAAO,aAAa,CAC/B,EAAW,EAAW,EAAS,aAAa,CAAC,2BAAmD,KAEtG,GAAI,EAAc,OAClB,EAAe,CAAA,EACF,GDXd,ACWc,EDXP,SAAS,CAAC,GAAG,CAAC,cCapB,IAAM,EAAkD,CAAC,EAKzD,OAHA,EAAK,UAAU,CAAG,EAAO,OAAO,CAAC,SAAS,CAC1C,EAAK,MAAM,CAAG,mBAEN,EAAK,UAAU,EACtB,IAAK,6BACJ,EAAK,KAAK,CAAG,aAAa,MAAM,CAAC,iBAAiB,CAClD,KAED,KAAK,qBACJ,EAAK,KAAK,CAAG,aAAa,MAAM,CAAC,gBAAgB,CACjD,KAED,KAAK,uBACJ,EAAK,KAAK,CAAG,aAAa,MAAM,CAAC,kBAAkB,CACnD,KAED,KAAK,kBACJ,EAAK,KAAK,CAAG,aAAa,MAAM,CAAC,aAAa,CAC9C,IAAM,EAAe,SAAS,aAAa,CAAC,4CAC5C,CAAA,EAAK,QAAQ,CAAG,EAAe,EAAa,KAAK,CAAG,EAEtD,CAEI,GAAU,AAAA,EAAW,GAEzB,OACE,IAAI,CAAC,CACL,IAAK,QACL,KAAM,OACN,SAAU,OACV,KAAM,CACP,GACC,MAAM,CAAC,SAAU,CAAC,EAClB,EAAe,CAAA,EACH,GD5Cf,AC4Ce,ED5CR,SAAS,CAAC,MAAM,CAAC,cC8CjB,GACH,ADtCE,SAAoB,CAAsB,EAChD,IAAM,EAAO,EAAM,IAAI,CAAG,EAAM,IAAI,CAAG,UACjC,EAAM,EAAM,GAAG,CAAG,EAAM,GAAG,CAAG,GAEpC,EAAM,EAAE,CAAC,SAAS,CAAC,GAAG,CAAC,MAAQ,GAC/B,EAAM,EAAE,CAAC,SAAS,CAAG,EACrB,EAAM,EAAE,CAAC,SAAS,CAAC,MAAM,CAAC,YAC3B,EC+BgB,CACV,GAAI,EACJ,KAAM,EAAE,OAAO,CAAG,UAAY,QAC9B,IAAK,EAAE,IAAI,AACZ,EAEF,EACF,E,C", "sources": ["<anon>", "assets-src/ts/settings.ts", "assets-src/ts/tabs.ts", "assets-src/ts/template-tags.ts", "assets-src/ts/utils.ts", "assets-src/ts/test-emails.ts", "assets-src/ts/conditional-fields.ts", "assets-src/ts/settings-form.ts"], "sourcesContent": ["(() => {\nfunction $f892de8327dc9e0f$export$2952830753ee3df9() {\n    const tabLinkList = document.querySelectorAll(\".heatbox-tab-nav-item a\");\n    tabLinkList.forEach(function(tabLink) {\n        tabLink.addEventListener(\"click\", function(e) {\n            const href = tabLink.href;\n            const hrefValue = href ? href.split(\"#\")[1] : \"\";\n            $f892de8327dc9e0f$var$switchTab(hrefValue);\n        });\n    });\n    window.addEventListener(\"load\", function(_e) {\n        let hash = window.location.hash;\n        hash = hash ? hash : \"#smtp\";\n        const hashValue = hash.replace(\"#\", \"\");\n        $f892de8327dc9e0f$var$switchTab(hashValue);\n    });\n}\nfunction $f892de8327dc9e0f$var$switchTab(tab) {\n    const tabLinkList = document.querySelectorAll(\".heatbox-tab-nav-item a\");\n    tabLinkList.forEach(function(tabLink) {\n        const parentEl = tabLink.parentElement;\n        if (!parentEl) return;\n        const href = tabLink.href;\n        const hrefValue = href ? href.split(\"#\")[1] : \"\";\n        if (hrefValue === tab) parentEl.classList.add(\"active\");\n        else parentEl.classList.remove(\"active\");\n    });\n    const tabContentList = document.querySelectorAll(\"[data-show-when-tab]\");\n    tabContentList.forEach(function(tabContent) {\n        const rawTabValue = tabContent.dataset.showWhenTab;\n        const tabValues = rawTabValue ? rawTabValue.split(\",\") : [];\n        const tabValuesTrimmed = tabValues.map((value)=>value.trim());\n        if (tabValuesTrimmed.includes(tab)) tabContent.style.display = \"block\";\n        else tabContent.style.display = \"none\";\n    });\n    $f892de8327dc9e0f$var$setRefererValue(tab);\n}\n/**\r\n * Set referer value for the tabs navigation of settings page.\r\n * This is being used to preserve the active tab after saving the settings page.\r\n *\r\n * @param {string} hashValue The hash value.\r\n */ function $f892de8327dc9e0f$var$setRefererValue(hashValue) {\n    const refererField = document.querySelector('[name=\"_wp_http_referer\"]');\n    if (refererField) {\n        if (refererField.value.includes(\"#\")) {\n            const urlSplits = refererField.value.split(\"#\");\n            const url = urlSplits[0];\n            refererField.value = url + \"#\" + hashValue;\n        } else refererField.value = refererField.value + \"#\" + hashValue;\n    }\n    const resetLink = document.querySelector(\".weed-reset-button\");\n    if (resetLink) {\n        if (resetLink.href.includes(\"#\")) {\n            const urlSplits = resetLink.href.split(\"#\");\n            const url = urlSplits[0];\n            resetLink.href = url + \"#\" + hashValue;\n        } else resetLink.href = resetLink.href + \"#\" + hashValue;\n    }\n}\n\n\nasync function $0670dfea8ff007c0$export$2cdf1b96a9f86d16(text) {\n    try {\n        await navigator.clipboard.writeText(text);\n    } catch (err) {\n        // console.error(\"Unable to copy text to clipboard:\", err);\n        await $0670dfea8ff007c0$export$89296fcf373e3bb8(text);\n    }\n}\nasync function $0670dfea8ff007c0$export$89296fcf373e3bb8(text) {\n    const textArea = document.createElement(\"textarea\");\n    textArea.value = text;\n    textArea.style.position = \"fixed\";\n    textArea.style.top = \"-3px\";\n    textArea.style.left = \"-3px\";\n    textArea.style.width = \"1px\";\n    textArea.style.height = \"1px\";\n    textArea.style.background = \"transparent\";\n    textArea.style.opacity = \"0\";\n    document.body.appendChild(textArea);\n    textArea.focus();\n    textArea.select();\n    try {\n        document.execCommand(\"copy\");\n    } catch (err) {\n        console.error(\"Unable to copy text to clipboard:\", err);\n    }\n    document.body.removeChild(textArea);\n}\nfunction $0670dfea8ff007c0$export$fea330ef6103f535(button) {\n    if (!button) return;\n    button.classList.add(\"is-loading\");\n}\nfunction $0670dfea8ff007c0$export$4dabcd056c1d895c(button) {\n    if (!button) return;\n    button.classList.remove(\"is-loading\");\n}\nfunction $0670dfea8ff007c0$export$b25305d7fdb340d7(props) {\n    const type = props.type ? props.type : \"success\";\n    const msg = props.msg ? props.msg : \"\";\n    props.el.classList.add(\"is-\" + type);\n    props.el.innerHTML = msg;\n    props.el.classList.remove(\"is-hidden\");\n}\nfunction $0670dfea8ff007c0$export$3bddf9505d6a7871(el) {\n    el.classList.add(\"is-hidden\");\n    el.classList.remove(\"is-success\");\n    el.classList.remove(\"is-error\");\n    el.classList.remove(\"is-failed\");\n    el.classList.remove(\"is-warning\");\n    el.classList.remove(\"is-info\");\n    el.innerHTML = \"\";\n}\n\n\nfunction $b3f9777c5cd7a583$export$68e1a451ce9a75b() {\n    init();\n    function init() {\n        const metabox = document.querySelector(\".tags-heatbox\");\n        if (!metabox) return;\n        const tags = metabox.querySelectorAll(\"code\");\n        if (!tags.length) return;\n        tags.forEach((tag)=>{\n            tag.addEventListener(\"click\", handleTagClick);\n        });\n    }\n    async function handleTagClick(e) {\n        const tag = e.target;\n        if (!tag) return;\n        const value = tag.innerText;\n        if (!value) return;\n        // Copy value to clipboard.\n        await (0, $0670dfea8ff007c0$export$2cdf1b96a9f86d16)(value);\n        const notice = document.querySelector(\".tags-heatbox .action-status\");\n        if (!notice) return;\n        notice.classList.add(\"is-shown\");\n        setTimeout(()=>{\n            notice.classList.remove(\"is-shown\");\n        }, 1500);\n    }\n}\n\n\n\nfunction $b7fa599c6ece7e06$export$f5ae4047b516e509() {\n    let isRequesting = false;\n    init();\n    function init() {\n        // @ts-ignore\n        jQuery(document).on(\"click\", \".weed-test-email-button\", sendTestEmail);\n    }\n    /**\r\n\t * Send test email via ajax request.\r\n\t */ function sendTestEmail(e) {\n        e.preventDefault();\n        // @ts-ignore\n        const button = this;\n        if (!button) return;\n        const allNoticeEls = document.querySelectorAll(\".weed-submission-notice\");\n        allNoticeEls.forEach(function(el) {\n            (0, $0670dfea8ff007c0$export$3bddf9505d6a7871)(el);\n        });\n        const parentEl = button.parentElement;\n        const noticeEl = parentEl ? parentEl.querySelector(\".weed-submission-notice\") : null;\n        if (isRequesting) return;\n        isRequesting = true;\n        (0, $0670dfea8ff007c0$export$fea330ef6103f535)(button);\n        const data = {};\n        data.email_type = button.dataset.emailType;\n        data.action = \"weed_test_emails\";\n        switch(data.email_type){\n            case \"admin_new_user_notif_email\":\n                data.nonce = weedSettings.nonces.adminWelcomeEmail;\n                break;\n            case \"user_welcome_email\":\n                data.nonce = weedSettings.nonces.userWelcomeEmail;\n                break;\n            case \"reset_password_email\":\n                data.nonce = weedSettings.nonces.resetPasswordEmail;\n                break;\n            case \"test_smtp_email\":\n                data.nonce = weedSettings.nonces.testSmtpEmail;\n                const toEmailField = document.querySelector(\"#weed_settings--test_smtp_recipient_email\");\n                data.to_email = toEmailField ? toEmailField.value : \"\";\n                break;\n        }\n        if (noticeEl) (0, $0670dfea8ff007c0$export$3bddf9505d6a7871)(noticeEl);\n        jQuery.ajax({\n            url: ajaxurl,\n            type: \"post\",\n            dataType: \"json\",\n            data: data\n        }).always(function(r) {\n            isRequesting = false;\n            (0, $0670dfea8ff007c0$export$4dabcd056c1d895c)(button);\n            if (noticeEl) (0, $0670dfea8ff007c0$export$b25305d7fdb340d7)({\n                el: noticeEl,\n                type: r.success ? \"success\" : \"error\",\n                msg: r.data\n            });\n        });\n    }\n}\n\n\nfunction $1ed547093d80174e$export$ba3937e898758160() {\n    const showWhenCheckedEls = document.querySelectorAll(\"[data-show-when-checked]\");\n    const fields = [];\n    const fieldNames = [];\n    const conditionalEls = [];\n    showWhenCheckedEls.forEach(function(showWhenCheckedEl) {\n        const rawFieldSelector = showWhenCheckedEl.dataset.showWhenChecked;\n        const fieldName = rawFieldSelector ? rawFieldSelector : \"\";\n        if (!fieldName) return;\n        const field = document.querySelector('[name=\"' + fieldName + '\"]');\n        if (!field) return;\n        if (!fieldNames.includes(fieldName)) {\n            fieldNames.push(fieldName);\n            fields.push(field);\n        }\n        conditionalEls.push(showWhenCheckedEl);\n        if (field.checked) showWhenCheckedEl.style.display = \"block\";\n        else showWhenCheckedEl.style.display = \"none\";\n    });\n    fields.forEach(function(field) {\n        field.addEventListener(\"change\", function(e) {\n            if (field.checked) conditionalEls.forEach(function(conditionalEl) {\n                const rawFieldSelector = conditionalEl.dataset.showWhenChecked;\n                const fieldName = rawFieldSelector ? rawFieldSelector : \"\";\n                if (!fieldName) return;\n                if (fieldName === field.name) conditionalEl.style.display = \"block\";\n            });\n            else conditionalEls.forEach(function(conditionalEl) {\n                const rawFieldSelector = conditionalEl.dataset.showWhenChecked;\n                const fieldName = rawFieldSelector ? rawFieldSelector : \"\";\n                if (!fieldName) return;\n                if (fieldName === field.name) conditionalEl.style.display = \"none\";\n            });\n        });\n    });\n    const smtpEncryptionFields = document.querySelectorAll('[name=\"weed_settings[smtp_encryption]\"]');\n    smtpEncryptionFields.forEach(function(smtpEncryptionField) {\n        smtpEncryptionField.addEventListener(\"change\", handleSmtpEncryptionFieldChange);\n    });\n    function handleSmtpEncryptionFieldChange(e) {\n        const value = e.target.value;\n        const smtpPortField = document.querySelector('[name=\"weed_settings[smtp_port]\"]');\n        if (!smtpPortField) return;\n        if (value === \"ssl\") smtpPortField.value = \"465\";\n        else if (value === \"tls\") smtpPortField.value = \"587\";\n        else smtpPortField.value = \"25\";\n    }\n}\n\n\nfunction $3fb511a7894c3583$export$2e2bcd8739ae039() {\n    // @ts-ignore\n    jQuery(document).on(\"click\", \".weed-reset-settings-button\", $3fb511a7894c3583$var$resetSettings);\n}\nfunction $3fb511a7894c3583$var$resetSettings(e) {\n    if (!confirm(weedSettings.warningMessages.resetSettings)) e.preventDefault();\n}\n\n\n(0, $3fb511a7894c3583$export$2e2bcd8739ae039)();\n(0, $f892de8327dc9e0f$export$2952830753ee3df9)();\n(0, $b3f9777c5cd7a583$export$68e1a451ce9a75b)();\n(0, $1ed547093d80174e$export$ba3937e898758160)();\n(0, $b7fa599c6ece7e06$export$f5ae4047b516e509)();\n\n})();\n//# sourceMappingURL=settings.js.map\n", "import {setupTabs} from \"./tabs\";\r\nimport {setupTemplateTags} from \"./template-tags\";\r\nimport {setupTestEmails} from \"./test-emails\";\r\nimport {setupConditionalFields} from \"./conditional-fields\";\r\nimport setupSettingsForm from \"./settings-form\";\r\n\r\nsetupSettingsForm();\r\nsetupTabs();\r\nsetupTemplateTags();\r\nsetupConditionalFields();\r\nsetupTestEmails();\r\n", "export function setupTabs(): void {\r\n\tconst tabLinkList = document.querySelectorAll(\r\n\t\t\".heatbox-tab-nav-item a\",\r\n\t) as NodeListOf<HTMLAnchorElement>;\r\n\r\n\ttabLinkList.forEach(function (tabLink) {\r\n\t\ttabLink.addEventListener(\"click\", function (e) {\r\n\t\t\tconst href = tabLink.href;\r\n\t\t\tconst hrefValue = href ? href.split(\"#\")[1] : \"\";\r\n\t\t\tswitchTab(hrefValue);\r\n\t\t});\r\n\t});\r\n\r\n\twindow.addEventListener(\"load\", function (_e) {\r\n\t\tlet hash = window.location.hash;\r\n\t\thash = hash ? hash : \"#smtp\";\r\n\t\tconst hashValue = hash.replace(\"#\", \"\");\r\n\r\n\t\tswitchTab(hashValue);\r\n\t});\r\n}\r\n\r\nfunction switchTab(tab: string): void {\r\n\tconst tabLinkList = document.querySelectorAll(\r\n\t\t\".heatbox-tab-nav-item a\",\r\n\t) as NodeListOf<HTMLAnchorElement>;\r\n\r\n\ttabLinkList.forEach(function (tabLink) {\r\n\t\tconst parentEl = tabLink.parentElement;\r\n\t\tif (!parentEl) return;\r\n\r\n\t\tconst href = tabLink.href;\r\n\t\tconst hrefValue = href ? href.split(\"#\")[1] : \"\";\r\n\r\n\t\tif (hrefValue === tab) {\r\n\t\t\tparentEl.classList.add(\"active\");\r\n\t\t} else {\r\n\t\t\tparentEl.classList.remove(\"active\");\r\n\t\t}\r\n\t});\r\n\r\n\tconst tabContentList = document.querySelectorAll(\r\n\t\t\"[data-show-when-tab]\",\r\n\t) as NodeListOf<HTMLElement>;\r\n\r\n\ttabContentList.forEach(function (tabContent) {\r\n\t\tconst rawTabValue = tabContent.dataset.showWhenTab;\r\n\t\tconst tabValues = rawTabValue ? rawTabValue.split(\",\") : [];\r\n\t\tconst tabValuesTrimmed = tabValues.map((value) => value.trim());\r\n\r\n\t\tif (tabValuesTrimmed.includes(tab)) {\r\n\t\t\ttabContent.style.display = \"block\";\r\n\t\t} else {\r\n\t\t\ttabContent.style.display = \"none\";\r\n\t\t}\r\n\t});\r\n\r\n\tsetRefererValue(tab);\r\n}\r\n\r\n/**\r\n * Set referer value for the tabs navigation of settings page.\r\n * This is being used to preserve the active tab after saving the settings page.\r\n *\r\n * @param {string} hashValue The hash value.\r\n */\r\nfunction setRefererValue(hashValue: string) {\r\n\tconst refererField = document.querySelector('[name=\"_wp_http_referer\"]') as HTMLInputElement | null;\r\n\r\n\tif (refererField) {\r\n\t\tif (refererField.value.includes(\"#\")) {\r\n\t\t\tconst urlSplits = refererField.value.split(\"#\");\r\n\t\t\tconst url = urlSplits[0];\r\n\r\n\t\t\trefererField.value = url + \"#\" + hashValue;\r\n\t\t} else {\r\n\t\t\trefererField.value = refererField.value + \"#\" + hashValue;\r\n\t\t}\r\n\t}\r\n\r\n\tconst resetLink = document.querySelector('.weed-reset-button') as HTMLAnchorElement | null;\r\n\r\n\tif (resetLink) {\r\n\t\tif (resetLink.href.includes(\"#\")) {\r\n\t\t\tconst urlSplits = resetLink.href.split(\"#\");\r\n\t\t\tconst url = urlSplits[0];\r\n\r\n\t\t\tresetLink.href = url + \"#\" + hashValue;\r\n\t\t} else {\r\n\t\t\tresetLink.href = resetLink.href + \"#\" + hashValue;\r\n\t\t}\r\n\t}\r\n}\r\n", "import { copyToClipboard } from \"./utils\";\n\nexport function setupTemplateTags() {\n\tinit();\n\n\tfunction init() {\n\t\tconst metabox = document.querySelector(\".tags-heatbox\");\n\t\tif (!metabox) return;\n\n\t\tconst tags = metabox.querySelectorAll(\"code\");\n\t\tif (!tags.length) return;\n\n\t\ttags.forEach((tag) => {\n\t\t\ttag.addEventListener(\"click\", handleTagClick);\n\t\t});\n\t}\n\n\tasync function handleTagClick(e: Event) {\n\t\tconst tag = e.target as HTMLElement | null;\n\t\tif (!tag) return;\n\n\t\tconst value = tag.innerText;\n\t\tif (!value) return;\n\n\t\t// Copy value to clipboard.\n\t\tawait copyToClipboard(value);\n\n\t\tconst notice = document.querySelector(\n\t\t\t\".tags-heatbox .action-status\"\n\t\t) as HTMLElement | null;\n\t\tif (!notice) return;\n\n\t\tnotice.classList.add(\"is-shown\");\n\n\t\tsetTimeout(() => {\n\t\t\tnotice.classList.remove(\"is-shown\");\n\t\t}, 1500);\n\t}\n}\n", "export async function copyToClipboard(text: string) {\r\n\ttry {\r\n\t\tawait navigator.clipboard.writeText(text);\r\n\t} catch (err) {\r\n\t\t// console.error(\"Unable to copy text to clipboard:\", err);\r\n\t\tawait copyToClipboardViaExecCommand(text);\r\n\t}\r\n}\r\n\r\nexport async function copyToClipboardViaExecCommand(text: string) {\r\n\tconst textArea = document.createElement(\"textarea\");\r\n\r\n\ttextArea.value = text;\r\n\ttextArea.style.position = \"fixed\";\r\n\ttextArea.style.top = \"-3px\";\r\n\ttextArea.style.left = \"-3px\";\r\n\ttextArea.style.width = \"1px\";\r\n\ttextArea.style.height = \"1px\";\r\n\ttextArea.style.background = \"transparent\";\r\n\ttextArea.style.opacity = \"0\";\r\n\r\n\tdocument.body.appendChild(textArea);\r\n\ttextArea.focus();\r\n\ttextArea.select();\r\n\r\n\ttry {\r\n\t\tdocument.execCommand(\"copy\");\r\n\t} catch (err) {\r\n\t\tconsole.error(\"Unable to copy text to clipboard:\", err);\r\n\t}\r\n\r\n\tdocument.body.removeChild(textArea);\r\n}\r\n\r\nexport function startLoading(button: HTMLButtonElement | null) {\r\n\tif (!button) return;\r\n\tbutton.classList.add(\"is-loading\");\r\n}\r\n\r\nexport function stopLoading(button: HTMLButtonElement | null) {\r\n\tif (!button) return;\r\n\tbutton.classList.remove(\"is-loading\");\r\n}\r\n\r\nexport type HideNoticeProps = {\r\n\tel: HTMLElement;\r\n\ttype?: 'success' | 'error' | 'failed' | 'warning' | 'info';\r\n\tmsg?: string;\r\n}\r\n\r\nexport function showNotice(props: HideNoticeProps) {\r\n\tconst type = props.type ? props.type : 'success';\r\n\tconst msg = props.msg ? props.msg : '';\r\n\r\n\tprops.el.classList.add(\"is-\" + type);\r\n\tprops.el.innerHTML = msg;\r\n\tprops.el.classList.remove(\"is-hidden\");\r\n}\r\n\r\nexport function hideNotice(el: HTMLElement) {\r\n\tel.classList.add(\"is-hidden\");\r\n\tel.classList.remove(\"is-success\");\r\n\tel.classList.remove(\"is-error\");\r\n\tel.classList.remove(\"is-failed\");\r\n\tel.classList.remove(\"is-warning\");\r\n\tel.classList.remove(\"is-info\");\r\n\tel.innerHTML = '';\r\n}", "import {hideNotice, showNotice, startLoading, stopLoading} from \"./utils\";\r\n\r\ndeclare var ajaxurl: string;\r\n\r\ndeclare var weedSettings: {\r\n\tnonces: {\r\n\t\tadminWelcomeEmail: string;\r\n\t\tuserWelcomeEmail: string;\r\n\t\tresetPasswordEmail: string;\r\n\t\ttestSmtpEmail: string;\r\n\t};\r\n\twarningMessages: {\r\n\t\tresetSettings: string;\r\n\t};\r\n};\r\n\r\nexport function setupTestEmails() {\r\n\tlet isRequesting = false;\r\n\r\n\tinit();\r\n\r\n\tfunction init() {\r\n\t\t// @ts-ignore\r\n\t\tjQuery(document).on(\"click\", \".weed-test-email-button\", sendTestEmail);\r\n\t}\r\n\r\n\t/**\r\n\t * Send test email via ajax request.\r\n\t */\r\n\tfunction sendTestEmail(e: MouseEvent) {\r\n\t\te.preventDefault();\r\n\r\n\t\t// @ts-ignore\r\n\t\tconst button = this as HTMLButtonElement | null;\r\n\t\tif (!button) return;\r\n\r\n\t\tconst allNoticeEls = document.querySelectorAll('.weed-submission-notice') as NodeListOf<HTMLElement>;\r\n\r\n\t\tallNoticeEls.forEach(function (el) {\r\n\t\t\thideNotice(el);\r\n\t\t});\r\n\r\n\t\tconst parentEl = button.parentElement as HTMLElement | null;\r\n\t\tconst noticeEl = parentEl ? parentEl.querySelector('.weed-submission-notice') as HTMLElement | null : null;\r\n\r\n\t\tif (isRequesting) return;\r\n\t\tisRequesting = true;\r\n\t\tstartLoading(button);\r\n\r\n\t\tconst data: Record<string, string | undefined | null> = {};\r\n\r\n\t\tdata.email_type = button.dataset.emailType;\r\n\t\tdata.action = \"weed_test_emails\";\r\n\r\n\t\tswitch (data.email_type) {\r\n\t\t\tcase \"admin_new_user_notif_email\":\r\n\t\t\t\tdata.nonce = weedSettings.nonces.adminWelcomeEmail;\r\n\t\t\t\tbreak;\r\n\r\n\t\t\tcase \"user_welcome_email\":\r\n\t\t\t\tdata.nonce = weedSettings.nonces.userWelcomeEmail;\r\n\t\t\t\tbreak;\r\n\r\n\t\t\tcase \"reset_password_email\":\r\n\t\t\t\tdata.nonce = weedSettings.nonces.resetPasswordEmail;\r\n\t\t\t\tbreak;\r\n\r\n\t\t\tcase \"test_smtp_email\":\r\n\t\t\t\tdata.nonce = weedSettings.nonces.testSmtpEmail;\r\n\t\t\t\tconst toEmailField = document.querySelector('#weed_settings--test_smtp_recipient_email') as HTMLInputElement;\r\n\t\t\t\tdata.to_email = toEmailField ? toEmailField.value : '';\r\n\t\t\t\tbreak;\r\n\t\t}\r\n\r\n\t\tif (noticeEl) hideNotice(noticeEl);\r\n\r\n\t\tjQuery\r\n\t\t\t.ajax({\r\n\t\t\t\turl: ajaxurl,\r\n\t\t\t\ttype: \"post\",\r\n\t\t\t\tdataType: \"json\",\r\n\t\t\t\tdata: data,\r\n\t\t\t})\r\n\t\t\t.always(function (r) {\r\n\t\t\t\tisRequesting = false;\r\n\t\t\t\tstopLoading(button);\r\n\r\n\t\t\t\tif (noticeEl) {\r\n\t\t\t\t\tshowNotice({\r\n\t\t\t\t\t\tel: noticeEl,\r\n\t\t\t\t\t\ttype: r.success ? 'success' : 'error',\r\n\t\t\t\t\t\tmsg: r.data,\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t}\r\n}", "export function setupConditionalFields() {\r\n\tconst showWhenCheckedEls = document.querySelectorAll(\r\n\t\t\"[data-show-when-checked]\",\r\n\t) as NodeListOf<HTMLElement>;\r\n\r\n\tconst fields: HTMLInputElement[] = [];\r\n\tconst fieldNames: string[] = [];\r\n\tconst conditionalEls: HTMLElement[] = [];\r\n\r\n\tshowWhenCheckedEls.forEach(function (showWhenCheckedEl) {\r\n\t\tconst rawFieldSelector = showWhenCheckedEl.dataset.showWhenChecked;\r\n\t\tconst fieldName = rawFieldSelector ? rawFieldSelector : \"\";\r\n\t\tif (!fieldName) return;\r\n\r\n\t\tconst field = document.querySelector(\r\n\t\t\t'[name=\"' + fieldName + '\"]',\r\n\t\t) as HTMLInputElement;\r\n\t\tif (!field) return;\r\n\r\n\t\tif (!fieldNames.includes(fieldName)) {\r\n\t\t\tfieldNames.push(fieldName);\r\n\t\t\tfields.push(field);\r\n\t\t}\r\n\r\n\t\tconditionalEls.push(showWhenCheckedEl);\r\n\r\n\t\tif (field.checked) {\r\n\t\t\tshowWhenCheckedEl.style.display = \"block\";\r\n\t\t} else {\r\n\t\t\tshowWhenCheckedEl.style.display = \"none\";\r\n\t\t}\r\n\t});\r\n\r\n\tfields.forEach(function (field) {\r\n\t\tfield.addEventListener(\"change\", function (e) {\r\n\t\t\tif (field.checked) {\r\n\t\t\t\tconditionalEls.forEach(function (conditionalEl) {\r\n\t\t\t\t\tconst rawFieldSelector = conditionalEl.dataset.showWhenChecked;\r\n\t\t\t\t\tconst fieldName = rawFieldSelector ? rawFieldSelector : \"\";\r\n\t\t\t\t\tif (!fieldName) return;\r\n\r\n\t\t\t\t\tif (fieldName === field.name) {\r\n\t\t\t\t\t\tconditionalEl.style.display = \"block\";\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t} else {\r\n\t\t\t\tconditionalEls.forEach(function (conditionalEl) {\r\n\t\t\t\t\tconst rawFieldSelector = conditionalEl.dataset.showWhenChecked;\r\n\t\t\t\t\tconst fieldName = rawFieldSelector ? rawFieldSelector : \"\";\r\n\t\t\t\t\tif (!fieldName) return;\r\n\r\n\t\t\t\t\tif (fieldName === field.name) {\r\n\t\t\t\t\t\tconditionalEl.style.display = \"none\";\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t});\r\n\t});\r\n\r\n\tconst smtpEncryptionFields = document.querySelectorAll('[name=\"weed_settings[smtp_encryption]\"]');\r\n\r\n\tsmtpEncryptionFields.forEach(function (smtpEncryptionField) {\r\n\t\tsmtpEncryptionField.addEventListener(\"change\", handleSmtpEncryptionFieldChange);\r\n\t});\r\n\r\n\tfunction handleSmtpEncryptionFieldChange(e: Event) {\r\n\t\tconst value = (e.target as HTMLInputElement).value;\r\n\t\tconst smtpPortField = document.querySelector('[name=\"weed_settings[smtp_port]\"]') as HTMLInputElement;\r\n\t\tif (!smtpPortField) return;\r\n\r\n\t\tif (value === \"ssl\") {\r\n\t\t\tsmtpPortField.value = \"465\";\r\n\t\t} else if (value === \"tls\") {\r\n\t\t\tsmtpPortField.value = \"587\";\r\n\t\t} else {\r\n\t\t\tsmtpPortField.value = \"25\";\r\n\t\t}\r\n\t}\r\n}\r\n", "declare var weedSettings: {\r\n\tnonces: {\r\n\t\tadminWelcomeEmail: string;\r\n\t\tuserWelcomeEmail: string;\r\n\t\tresetPasswordEmail: string;\r\n\t\ttestSmtpEmail: string;\r\n\t};\r\n\twarningMessages: {\r\n\t\tresetSettings: string;\r\n\t};\r\n};\r\n\r\nexport default function setupSettingsForm() {\r\n\t// @ts-ignore\r\n\tjQuery(document).on(\"click\", \".weed-reset-settings-button\", resetSettings);\r\n}\r\n\r\nfunction resetSettings(e: MouseEvent) {\r\n\tif (!confirm(weedSettings.warningMessages.resetSettings))\r\n\t\te.preventDefault();\r\n}\r\n"], "names": ["isRequesting", "$f892de8327dc9e0f$var$switchTab", "tab", "tabLinkList", "document", "querySelectorAll", "for<PERSON>ach", "tabLink", "parentEl", "parentElement", "href", "hrefValue", "split", "classList", "add", "remove", "tabContentList", "tab<PERSON>ontent", "rawTabValue", "dataset", "showWhenTab", "tabValuesTrimmed", "tabValues", "map", "value", "trim", "includes", "style", "display", "$f892de8327dc9e0f$var$setRefererValue", "hashValue", "refererField", "querySelector", "url", "urlSplits", "resetLink", "$0670dfea8ff007c0$export$2cdf1b96a9f86d16", "text", "navigator", "clipboard", "writeText", "err", "$0670dfea8ff007c0$export$89296fcf373e3bb8", "textArea", "createElement", "position", "top", "left", "width", "height", "background", "opacity", "body", "append<PERSON><PERSON><PERSON>", "focus", "select", "execCommand", "console", "error", "<PERSON><PERSON><PERSON><PERSON>", "$0670dfea8ff007c0$export$3bddf9505d6a7871", "el", "innerHTML", "j<PERSON><PERSON><PERSON>", "on", "e", "confirm", "weedSettings", "warningMessages", "resetSettings", "preventDefault", "addEventListener", "window", "_e", "hash", "location", "replace", "init", "metabox", "tags", "length", "tag", "handleTagClick", "target", "innerText", "notice", "setTimeout", "showWhenCheckedEls", "fields", "fieldNames", "conditionalEls", "handleSmtpEncryptionFieldChange", "smtpPortField", "showWhenCheckedEl", "fieldName", "rawFieldSelector", "showWhenChecked", "field", "push", "checked", "conditionalEl", "name", "smtpEncryptionFields", "smtpEncryptionField", "button", "allNoticeEls", "noticeEl", "data", "email_type", "emailType", "action", "nonce", "nonces", "adminWelcomeEmail", "userWelcomeEmail", "resetPasswordEmail", "testSmtpEmail", "toEmailField", "to_email", "ajax", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type", "dataType", "always", "r", "props", "msg", "success"], "version": 3, "file": "settings.js.map"}