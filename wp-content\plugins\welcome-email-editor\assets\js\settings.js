(()=>{let e;function t(e){document.querySelectorAll(".heatbox-tab-nav-item a").forEach(function(t){let n=t.parentElement;if(!n)return;let s=t.href;(s?s.split("#")[1]:"")===e?n.classList.add("active"):n.classList.remove("active")}),document.querySelectorAll("[data-show-when-tab]").forEach(function(t){let n=t.dataset.showWhenTab;(n?n.split(","):[]).map(e=>e.trim()).includes(e)?t.style.display="block":t.style.display="none"}),function(e){let t=document.querySelector('[name="_wp_http_referer"]');if(t){if(t.value.includes("#")){let n=t.value.split("#")[0];t.value=n+"#"+e}else t.value=t.value+"#"+e}let n=document.querySelector(".weed-reset-button");if(n){if(n.href.includes("#")){let t=n.href.split("#")[0];n.href=t+"#"+e}else n.href=n.href+"#"+e}}(e)}async function n(e){try{await navigator.clipboard.writeText(e)}catch(t){await s(e)}}async function s(e){let t=document.createElement("textarea");t.value=e,t.style.position="fixed",t.style.top="-3px",t.style.left="-3px",t.style.width="1px",t.style.height="1px",t.style.background="transparent",t.style.opacity="0",document.body.appendChild(t),t.focus(),t.select();try{document.execCommand("copy")}catch(e){console.error("Unable to copy text to clipboard:",e)}document.body.removeChild(t)}function a(e){e.classList.add("is-hidden"),e.classList.remove("is-success"),e.classList.remove("is-error"),e.classList.remove("is-failed"),e.classList.remove("is-warning"),e.classList.remove("is-info"),e.innerHTML=""}jQuery(document).on("click",".weed-reset-settings-button",function(e){confirm(weedSettings.warningMessages.resetSettings)||e.preventDefault()}),document.querySelectorAll(".heatbox-tab-nav-item a").forEach(function(e){e.addEventListener("click",function(n){let s=e.href;t(s?s.split("#")[1]:"")})}),window.addEventListener("load",function(e){let n=window.location.hash;t((n=n||"#smtp").replace("#",""))}),function(){(function(){let t=document.querySelector(".tags-heatbox");if(!t)return;let n=t.querySelectorAll("code");n.length&&n.forEach(t=>{t.addEventListener("click",e)})})();async function e(e){let t=e.target;if(!t)return;let s=t.innerText;if(!s)return;await n(s);let a=document.querySelector(".tags-heatbox .action-status");a&&(a.classList.add("is-shown"),setTimeout(()=>{a.classList.remove("is-shown")},1500))}}(),function(){let e=document.querySelectorAll("[data-show-when-checked]"),t=[],n=[],s=[];function a(e){let t=e.target.value,n=document.querySelector('[name="weed_settings[smtp_port]"]');n&&("ssl"===t?n.value="465":"tls"===t?n.value="587":n.value="25")}e.forEach(function(e){let a=e.dataset.showWhenChecked||"";if(!a)return;let l=document.querySelector('[name="'+a+'"]');l&&(n.includes(a)||(n.push(a),t.push(l)),s.push(e),l.checked?e.style.display="block":e.style.display="none")}),t.forEach(function(e){e.addEventListener("change",function(t){e.checked?s.forEach(function(t){let n=t.dataset.showWhenChecked||"";n&&n===e.name&&(t.style.display="block")}):s.forEach(function(t){let n=t.dataset.showWhenChecked||"";n&&n===e.name&&(t.style.display="none")})})}),document.querySelectorAll('[name="weed_settings[smtp_encryption]"]').forEach(function(e){e.addEventListener("change",a)})}(),e=!1,jQuery(document).on("click",".weed-test-email-button",function(t){t.preventDefault();let n=this;if(!n)return;document.querySelectorAll(".weed-submission-notice").forEach(function(e){a(e)});let s=n.parentElement,l=s?s.querySelector(".weed-submission-notice"):null;if(e)return;e=!0,n&&n.classList.add("is-loading");let c={};switch(c.email_type=n.dataset.emailType,c.action="weed_test_emails",c.email_type){case"admin_new_user_notif_email":c.nonce=weedSettings.nonces.adminWelcomeEmail;break;case"user_welcome_email":c.nonce=weedSettings.nonces.userWelcomeEmail;break;case"reset_password_email":c.nonce=weedSettings.nonces.resetPasswordEmail;break;case"test_smtp_email":c.nonce=weedSettings.nonces.testSmtpEmail;let i=document.querySelector("#weed_settings--test_smtp_recipient_email");c.to_email=i?i.value:""}l&&a(l),jQuery.ajax({url:ajaxurl,type:"post",dataType:"json",data:c}).always(function(t){e=!1,n&&n.classList.remove("is-loading"),l&&function(e){let t=e.type?e.type:"success",n=e.msg?e.msg:"";e.el.classList.add("is-"+t),e.el.innerHTML=n,e.el.classList.remove("is-hidden")}({el:l,type:t.success?"success":"error",msg:t.data})})})})();
//# sourceMappingURL=settings.js.map
