$white-color: #fff;
$dark-grey: #444;
$light-grey: #f5492a;

@-webkit-keyframes loadingSpinAround {
	from {
		transform: rotate(0);
	}
	to {
		transform: rotate(359deg);
	}
}

@keyframes loadingSpinAround {
	from {
		transform: rotate(0);
	}
	to {
		transform: rotate(359deg);
	}
}

// Heatbox override
.heatbox-header {
	.logo-container > div:first-child {
		width: 70%;
	}

	.logo-container .subtitle {
		line-height: 1.4;
	}

	.logo-container > div:last-child {
		width: 30%;
	}
}

@media screen and (max-width: 990px) {
	.heatbox-sidebar {
		margin-top: 20px;
	}
}

@media screen and (max-width: 780px) {
	.heatbox-header {
		.logo-container {
			.title {
				width: 100%;
			}

			.subtitle {
				width: 100%;
			}
		}
	}
}

@media screen and (max-width: 576px) {
	.heatbox-header {
		.logo-container img {
			width: 80px;
		}
	}
}

// Settings form
.weed-fields {
	position: relative;
}

.weed-field {
	margin-bottom: 15px;

	.wp-picker-input-wrap {
		.color-picker {
			width: 160px !important;
		}

		display: inline-flex;
		max-width: 220px;
		overflow: hidden;
		transition: max-width 0.2s;
	}

	.wp-picker-input-wrap.hidden {
		display: inline-flex;
		max-width: 0;
	}
}

.weed-inline-fields {
	.weed-field {
		&:last-child {
			margin-bottom: 0;
			margin-right: 0;
		}

		margin-right: 10px;
	}

	display: flex;
	position: relative;
}

.weed-label {
	display: block;
	margin-bottom: 3px;
}

.is-hidden {
	display: none;
}

@media screen and (max-width: 320px) {
	.weed-radio-fields .radio-label {
		display: block;
		margin-bottom: 10px;
	}
}

// Buttons
.button.weed-test-email-button {
	display: flex;
	position: relative;
	align-items: center;
	justify-content: center;
}

.weed-test-email-button.is-loading {
	color: transparent !important;
	pointer-events: none;

	&::after {
		content: "";
		position: absolute;
		width: 1em;
		display: block;
		height: 1em;
		border-width: 2px;
		border-style: solid;
		border-color: transparent transparent #fff #fff !important;
		border-radius: 290486px;
		-webkit-animation: loadingSpinAround 0.5s infinite linear;
		animation: loadingSpinAround 0.5s infinite linear;
	}
}

.weed-buttons {
	.weed-reset-button {
		margin-left: 7px;
		color: $white-color;
		background-color: tomato;
		border-color: tomato;
		text-align: center;

		&:hover {
			color: $white-color;
			background-color: $light-grey;
			border-color: $light-grey;
		}

		&:active {
			color: $white-color;
			background-color: $light-grey;
			border-color: $light-grey;
		}

		&:focus {
			color: $white-color;
			background-color: $light-grey;
			border-color: $light-grey;
			box-shadow: 0 0 0 1px #fff, 0 0 0 3px #f5492a;
		}
	}

	@media screen and (max-width: 320px) {
		.button {
			display: block;
			width: 100%;
		}

		.weed-reset-button {
			margin-left: 0;
			margin-top: 10px;
		}
	}
}

// Code Mirror
.CodeMirror-wrap {
	border: 1px solid #ddd;
}

// Test SMTP metabox.
.weed-test-smtp-metabox {
	th {
		display: none;
	}

	.form-table td p {
		margin-bottom: 20px;
	}
}

.weed-submission-notice {
	padding: 5px 10px;
	font-weight: 700;
	margin-bottom: 15px;
	border-width: 2px;
	border-style: solid;

	&.is-error {
		background-color: rgba(255, 99, 71, .1);
		border-color: tomato;
		color: tomato;
	}

	&.is-success {
		background-color: rgba(79, 225, 144, .1);
		border-color: #4fe190;
		color: #4fe190;
	}
}

// Featured products
.weed-featured-products {
	> h2 {
		text-align: center;
		margin-bottom: 50px;
		font-size: 24px;

		@media screen and (max-width: 576px) {
			line-height: 1.4;
		}
	}

	.products {
		display: flex;
		justify-content: space-between;
		flex-wrap: wrap;

		li {
			width: 32%;

			@media screen and (max-width: 780px) {
				width: 100%;
			}
		}

		.subheadline {
			font-weight: 600;
			margin-top: -6px;
		}

		img {
			max-width: 100%;
		}
	}

	.credit {
		margin-top: 20px;
		text-align: center;
		opacity: 0.5;
	}
}

// Resources metabox.
.weed-resources-metabox {
	ul {
		margin: 0;
	}

	li {
		margin-bottom: 15px;

		&:last-child {
			margin-bottom: 0;
		}
	}

	a {
		text-decoration: none;
	}
}
