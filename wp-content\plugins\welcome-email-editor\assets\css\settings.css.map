{"mappings": "ACIA,iFAoBC,0DAIA,0DAIA,yDAKD,iCACC,kCAKD,iCAGG,6FAWH,iCAEE,gDAOF,+BAIA,+BAGC,+GACC,sEAUD,yEAMD,mDACC,kDACC,0EAYF,4CAKA,wBAIA,iCACC,kEAOD,wGAOA,6EAIC,iQAgBA,0HAOC,wIAYA,4IAQD,iCACC,+CAKA,gEAQF,uCAMC,wCAIA,4DAKD,gHAOC,6FAMA,iGASA,+EAKC,iCALD,4CAUA,4FAKC,+CAGC,iCAHD,iDAQA,+EAKA,qDAKD,6EASA,oCAIA,8CAGC,sDAKD", "sources": ["settings.css", "assets-src/scss/settings.scss"], "sourcesContent": ["@keyframes loadingSpinAround {\n  from {\n    transform: rotate(0);\n  }\n\n  to {\n    transform: rotate(359deg);\n  }\n}\n\n.heatbox-header .logo-container > div:first-child {\n  width: 70%;\n}\n\n.heatbox-header .logo-container .subtitle {\n  line-height: 1.4;\n}\n\n.heatbox-header .logo-container > div:last-child {\n  width: 30%;\n}\n\n@media screen and (width <= 990px) {\n  .heatbox-sidebar {\n    margin-top: 20px;\n  }\n}\n\n@media screen and (width <= 780px) {\n  .heatbox-header .logo-container .title, .heatbox-header .logo-container .subtitle {\n    width: 100%;\n  }\n}\n\n@media screen and (width <= 576px) {\n  .heatbox-header .logo-container img {\n    width: 80px;\n  }\n}\n\n.weed-fields {\n  position: relative;\n}\n\n.weed-field {\n  margin-bottom: 15px;\n}\n\n.weed-field .wp-picker-input-wrap {\n  max-width: 220px;\n  transition: max-width .2s;\n  display: inline-flex;\n  overflow: hidden;\n}\n\n.weed-field .wp-picker-input-wrap .color-picker {\n  width: 160px !important;\n}\n\n.weed-field .wp-picker-input-wrap.hidden {\n  max-width: 0;\n  display: inline-flex;\n}\n\n.weed-inline-fields {\n  display: flex;\n  position: relative;\n}\n\n.weed-inline-fields .weed-field {\n  margin-right: 10px;\n}\n\n.weed-inline-fields .weed-field:last-child {\n  margin-bottom: 0;\n  margin-right: 0;\n}\n\n.weed-label {\n  margin-bottom: 3px;\n  display: block;\n}\n\n.is-hidden {\n  display: none;\n}\n\n@media screen and (width <= 320px) {\n  .weed-radio-fields .radio-label {\n    margin-bottom: 10px;\n    display: block;\n  }\n}\n\n.button.weed-test-email-button {\n  justify-content: center;\n  align-items: center;\n  display: flex;\n  position: relative;\n}\n\n.weed-test-email-button.is-loading {\n  pointer-events: none;\n  color: #0000 !important;\n}\n\n.weed-test-email-button.is-loading:after {\n  content: \"\";\n  border-style: solid;\n  border-width: 2px;\n  border-radius: 290486px;\n  width: 1em;\n  height: 1em;\n  animation: .5s linear infinite loadingSpinAround;\n  display: block;\n  position: absolute;\n  border-color: #0000 #0000 #fff #fff !important;\n}\n\n.weed-buttons .weed-reset-button {\n  color: #fff;\n  text-align: center;\n  background-color: tomato;\n  border-color: tomato;\n  margin-left: 7px;\n}\n\n.weed-buttons .weed-reset-button:hover, .weed-buttons .weed-reset-button:active {\n  color: #fff;\n  background-color: #f5492a;\n  border-color: #f5492a;\n}\n\n.weed-buttons .weed-reset-button:focus {\n  color: #fff;\n  background-color: #f5492a;\n  border-color: #f5492a;\n  box-shadow: 0 0 0 1px #fff, 0 0 0 3px #f5492a;\n}\n\n@media screen and (width <= 320px) {\n  .weed-buttons .button {\n    width: 100%;\n    display: block;\n  }\n\n  .weed-buttons .weed-reset-button {\n    margin-top: 10px;\n    margin-left: 0;\n  }\n}\n\n.CodeMirror-wrap {\n  border: 1px solid #ddd;\n}\n\n.weed-test-smtp-metabox th {\n  display: none;\n}\n\n.weed-test-smtp-metabox .form-table td p {\n  margin-bottom: 20px;\n}\n\n.weed-submission-notice {\n  border-style: solid;\n  border-width: 2px;\n  margin-bottom: 15px;\n  padding: 5px 10px;\n  font-weight: 700;\n}\n\n.weed-submission-notice.is-error {\n  color: tomato;\n  background-color: #ff63471a;\n  border-color: tomato;\n}\n\n.weed-submission-notice.is-success {\n  color: #4fe190;\n  background-color: #4fe1901a;\n  border-color: #4fe190;\n}\n\n.weed-featured-products > h2 {\n  text-align: center;\n  margin-bottom: 50px;\n  font-size: 24px;\n}\n\n@media screen and (width <= 576px) {\n  .weed-featured-products > h2 {\n    line-height: 1.4;\n  }\n}\n\n.weed-featured-products .products {\n  flex-wrap: wrap;\n  justify-content: space-between;\n  display: flex;\n}\n\n.weed-featured-products .products li {\n  width: 32%;\n}\n\n@media screen and (width <= 780px) {\n  .weed-featured-products .products li {\n    width: 100%;\n  }\n}\n\n.weed-featured-products .products .subheadline {\n  margin-top: -6px;\n  font-weight: 600;\n}\n\n.weed-featured-products .products img {\n  max-width: 100%;\n}\n\n.weed-featured-products .credit {\n  text-align: center;\n  opacity: .5;\n  margin-top: 20px;\n}\n\n.weed-resources-metabox ul {\n  margin: 0;\n}\n\n.weed-resources-metabox li {\n  margin-bottom: 15px;\n}\n\n.weed-resources-metabox li:last-child {\n  margin-bottom: 0;\n}\n\n.weed-resources-metabox a {\n  text-decoration: none;\n}\n/*# sourceMappingURL=settings.css.map */\n", "$white-color: #fff;\n$dark-grey: #444;\n$light-grey: #f5492a;\n\n@-webkit-keyframes loadingSpinAround {\n\tfrom {\n\t\ttransform: rotate(0);\n\t}\n\tto {\n\t\ttransform: rotate(359deg);\n\t}\n}\n\n@keyframes loadingSpinAround {\n\tfrom {\n\t\ttransform: rotate(0);\n\t}\n\tto {\n\t\ttransform: rotate(359deg);\n\t}\n}\n\n// Heatbox override\n.heatbox-header {\n\t.logo-container > div:first-child {\n\t\twidth: 70%;\n\t}\n\n\t.logo-container .subtitle {\n\t\tline-height: 1.4;\n\t}\n\n\t.logo-container > div:last-child {\n\t\twidth: 30%;\n\t}\n}\n\n@media screen and (max-width: 990px) {\n\t.heatbox-sidebar {\n\t\tmargin-top: 20px;\n\t}\n}\n\n@media screen and (max-width: 780px) {\n\t.heatbox-header {\n\t\t.logo-container {\n\t\t\t.title {\n\t\t\t\twidth: 100%;\n\t\t\t}\n\n\t\t\t.subtitle {\n\t\t\t\twidth: 100%;\n\t\t\t}\n\t\t}\n\t}\n}\n\n@media screen and (max-width: 576px) {\n\t.heatbox-header {\n\t\t.logo-container img {\n\t\t\twidth: 80px;\n\t\t}\n\t}\n}\n\n// Settings form\n.weed-fields {\n\tposition: relative;\n}\n\n.weed-field {\n\tmargin-bottom: 15px;\n\n\t.wp-picker-input-wrap {\n\t\t.color-picker {\n\t\t\twidth: 160px !important;\n\t\t}\n\n\t\tdisplay: inline-flex;\n\t\tmax-width: 220px;\n\t\toverflow: hidden;\n\t\ttransition: max-width 0.2s;\n\t}\n\n\t.wp-picker-input-wrap.hidden {\n\t\tdisplay: inline-flex;\n\t\tmax-width: 0;\n\t}\n}\n\n.weed-inline-fields {\n\t.weed-field {\n\t\t&:last-child {\n\t\t\tmargin-bottom: 0;\n\t\t\tmargin-right: 0;\n\t\t}\n\n\t\tmargin-right: 10px;\n\t}\n\n\tdisplay: flex;\n\tposition: relative;\n}\n\n.weed-label {\n\tdisplay: block;\n\tmargin-bottom: 3px;\n}\n\n.is-hidden {\n\tdisplay: none;\n}\n\n@media screen and (max-width: 320px) {\n\t.weed-radio-fields .radio-label {\n\t\tdisplay: block;\n\t\tmargin-bottom: 10px;\n\t}\n}\n\n// Buttons\n.button.weed-test-email-button {\n\tdisplay: flex;\n\tposition: relative;\n\talign-items: center;\n\tjustify-content: center;\n}\n\n.weed-test-email-button.is-loading {\n\tcolor: transparent !important;\n\tpointer-events: none;\n\n\t&::after {\n\t\tcontent: \"\";\n\t\tposition: absolute;\n\t\twidth: 1em;\n\t\tdisplay: block;\n\t\theight: 1em;\n\t\tborder-width: 2px;\n\t\tborder-style: solid;\n\t\tborder-color: transparent transparent #fff #fff !important;\n\t\tborder-radius: 290486px;\n\t\t-webkit-animation: loadingSpinAround 0.5s infinite linear;\n\t\tanimation: loadingSpinAround 0.5s infinite linear;\n\t}\n}\n\n.weed-buttons {\n\t.weed-reset-button {\n\t\tmargin-left: 7px;\n\t\tcolor: $white-color;\n\t\tbackground-color: tomato;\n\t\tborder-color: tomato;\n\t\ttext-align: center;\n\n\t\t&:hover {\n\t\t\tcolor: $white-color;\n\t\t\tbackground-color: $light-grey;\n\t\t\tborder-color: $light-grey;\n\t\t}\n\n\t\t&:active {\n\t\t\tcolor: $white-color;\n\t\t\tbackground-color: $light-grey;\n\t\t\tborder-color: $light-grey;\n\t\t}\n\n\t\t&:focus {\n\t\t\tcolor: $white-color;\n\t\t\tbackground-color: $light-grey;\n\t\t\tborder-color: $light-grey;\n\t\t\tbox-shadow: 0 0 0 1px #fff, 0 0 0 3px #f5492a;\n\t\t}\n\t}\n\n\t@media screen and (max-width: 320px) {\n\t\t.button {\n\t\t\tdisplay: block;\n\t\t\twidth: 100%;\n\t\t}\n\n\t\t.weed-reset-button {\n\t\t\tmargin-left: 0;\n\t\t\tmargin-top: 10px;\n\t\t}\n\t}\n}\n\n// Code Mirror\n.CodeMirror-wrap {\n\tborder: 1px solid #ddd;\n}\n\n// Test SMTP metabox.\n.weed-test-smtp-metabox {\n\tth {\n\t\tdisplay: none;\n\t}\n\n\t.form-table td p {\n\t\tmargin-bottom: 20px;\n\t}\n}\n\n.weed-submission-notice {\n\tpadding: 5px 10px;\n\tfont-weight: 700;\n\tmargin-bottom: 15px;\n\tborder-width: 2px;\n\tborder-style: solid;\n\n\t&.is-error {\n\t\tbackground-color: rgba(255, 99, 71, .1);\n\t\tborder-color: tomato;\n\t\tcolor: tomato;\n\t}\n\n\t&.is-success {\n\t\tbackground-color: rgba(79, 225, 144, .1);\n\t\tborder-color: #4fe190;\n\t\tcolor: #4fe190;\n\t}\n}\n\n// Featured products\n.weed-featured-products {\n\t> h2 {\n\t\ttext-align: center;\n\t\tmargin-bottom: 50px;\n\t\tfont-size: 24px;\n\n\t\t@media screen and (max-width: 576px) {\n\t\t\tline-height: 1.4;\n\t\t}\n\t}\n\n\t.products {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\tflex-wrap: wrap;\n\n\t\tli {\n\t\t\twidth: 32%;\n\n\t\t\t@media screen and (max-width: 780px) {\n\t\t\t\twidth: 100%;\n\t\t\t}\n\t\t}\n\n\t\t.subheadline {\n\t\t\tfont-weight: 600;\n\t\t\tmargin-top: -6px;\n\t\t}\n\n\t\timg {\n\t\t\tmax-width: 100%;\n\t\t}\n\t}\n\n\t.credit {\n\t\tmargin-top: 20px;\n\t\ttext-align: center;\n\t\topacity: 0.5;\n\t}\n}\n\n// Resources metabox.\n.weed-resources-metabox {\n\tul {\n\t\tmargin: 0;\n\t}\n\n\tli {\n\t\tmargin-bottom: 15px;\n\n\t\t&:last-child {\n\t\t\tmargin-bottom: 0;\n\t\t}\n\t}\n\n\ta {\n\t\ttext-decoration: none;\n\t}\n}\n"], "names": [], "version": 3, "file": "settings.css.map"}