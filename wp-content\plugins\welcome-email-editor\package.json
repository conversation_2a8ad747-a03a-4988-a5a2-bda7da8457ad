{"name": "swift-smtp", "version": "1.0.0", "description": "Dev tooling for Swift SMTP", "keywords": [], "author": "<PERSON>", "license": "GPL-3.0-or-later", "alias": {"jquery": {"global": "j<PERSON><PERSON><PERSON>"}}, "devDependencies": {"@parcel/transformer-sass": "2.12.0", "@types/jquery": "^3.5.30", "parcel": "^2.12.0", "prettier": "npm:wp-prettier@^3.0.3", "typescript": "^5.4.5"}, "dependencies": {"jquery": "^3.7.1", "vanjs-core": "^1.5.0"}, "scripts": {"watch-js": "parcel watch assets-src/ts/settings.ts --dist-dir assets/js", "watch-css": "parcel watch assets-src/scss/settings.css --dist-dir assets/css", "watch": "npm run watch-js & npm run watch-css", "build-js": "parcel build assets-src/ts/settings.ts --dist-dir assets/js", "build-css": "parcel build assets-src/scss/settings.scss --dist-dir assets/css", "build": "npm run build-js && npm run build-css"}}